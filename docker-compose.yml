services:
  frontend:
    build: ./src/frontend
    ports:
      - "3001:80"
    depends_on:
      - backend-java
      - backend-python

  backend-java:
    build: ./src/backend
    ports:
      - "8081:8081"
    environment:
      SPRING_DATASOURCE_URL: *******************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: root
    depends_on:
      - mysql

  backend-python:
    build: ./src/gemini-python-service
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: mysql+pymysql://root:root@mysql/aierpdb
    depends_on:
      - mysql

  mysql:
    image: mysql:8
    restart: always
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: aierpdb
