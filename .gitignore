# Dependencies
node_modules/
**/node_modules/
venv/
**/venv/
__pycache__/
**/__pycache__/
.pnpm-store/
**/.pnpm-store/
.pnpm-debug.log
**/.pnpm-debug.log

# Python dependencies
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Environment variables (keeping for reference but not used in this project)
.env
.env.local
.env.*
*.env

# Build outputs
dist/
**/dist/
build/
**/build/
.vite/
**/.vite/
.next/
**/.next/
out/
**/out/

# Maven specific
target/
**/target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
**/.mvn/

# IDE specific files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
.factorypath
.metadata/
.recommenders/
nbproject/
nbactions.xml
nb-configuration.xml
.nb-gradle/
.eclipse/

# Operating System Files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
hs_err_pid*

# Testing
coverage/
.pytest_cache/

# Database
*.sqlite3
mysql-data/

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.cache
.eslintcache
.stylelintcache
.sass-cache/
.cache/
.parcel-cache/

# Python virtual environments
.venv/

# Sensitive files (commented out since credentials are now embedded in code for private repo)
# **/application-production.properties
# **/application-local.properties
# **/application-dev.properties
# **/secrets.properties
# **/credentials.json
# **/key.json
# **/service-account*.json
# *secrets*/
# *credential*/

# Deployment scripts
scripts/deploy-*.ps1
scripts/deploy-*.sh
scripts/*-cloudrun*.ps1
scripts/*-cloudrun*.sh

# Runtime files
*.pid
*.seed
*.pid.lock

# Large files
*.psd
*.pdf
*.zip
*.rar
*.7z
*.tar
*.gz
*.mp4
*.mov
*.avi
*.wmv
*.iso
*.dmg
# *.jar  # Commented out - we need JAR files for Docker builds
*.war
*.ear

# Allow specific JAR files needed for Docker builds
!src/backend/target/*.jar

# Frontend specific
src/frontend/.pnpm-store/
src/frontend/.pnpm-debug.log
src/frontend/dist/
src/frontend/build/
src/frontend/.vite/

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Package manager lock files (keep pnpm-lock.yaml but ignore others)
package-lock.json
yarn.lock

# Specific large files detected by GitHub
510c168ab8e9f7f19c193bc73bcae52290664a2d
29c917202f5db259a88d2bbca1d9714d598ecfef
