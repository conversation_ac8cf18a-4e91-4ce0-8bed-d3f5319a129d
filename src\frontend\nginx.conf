server {
  listen 80;
  server_name localhost;

  root /usr/share/nginx/html;
  index index.html index.htm;

  # Handle React Router - all routes should serve index.html
  location / {
    try_files $uri $uri/ /index.html;
  }

  # Health check endpoint
  location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
  }

  # Cache static assets
  location ~* \.(?:ico|css|js|gif|jpe?g|png|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
  }
}
