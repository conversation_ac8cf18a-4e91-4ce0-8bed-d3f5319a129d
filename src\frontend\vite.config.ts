import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      "@": "/src", // Relative to project root
    },
  },
  server: {
    host: true, // Allow external connections
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '.ngrok.io',
      '.ngrok-free.app',
      '.ngrok.app'
    ],
    proxy: {
      // Proxy API requests starting with /api to the production backend server
      '/api': {
        target: 'https://backend-b2-backend-java-service-1055395134.us-central1.run.app', // Production backend URL
        changeOrigin: true, // Needed for virtual hosted sites
        secure: true,       // HTTPS backend
        // Optional: Rewrite path if needed (e.g., remove /api prefix)
        // rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
});