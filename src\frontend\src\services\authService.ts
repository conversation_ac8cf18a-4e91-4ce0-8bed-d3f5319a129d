// Removed unused imports
// const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api';

// Simplified auth service - no authentication required
// These functions are kept for compatibility but don't perform authentication

// Types (kept for compatibility)
export interface LoginRequest {
  username: string;
  password: string;
}

export interface JwtResponse {
  token: string;
  type: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

export interface SignupRequest {
  username: string;
  email: string;
  password: string;
  roles: string[];
}

// Mock login function - always succeeds and redirects to dashboard
export const login = async (loginRequest: LoginRequest): Promise<JwtResponse> => {
  // No actual authentication - just return a mock response
  return {
    token: 'mock-token',
    type: 'Bearer',
    id: 1,
    username: loginRequest.username,
    email: '<EMAIL>',
    roles: ['USER']
  };
};

// Mock register function
export const register = async (signupRequest: SignupRequest): Promise<any> => {
  // Mock implementation - just return success
  console.log('Mock registration for:', signupRequest.username);
  return { message: 'Registration successful' };
};

// No-op logout function
export const logout = (): void => {
  // No authentication to clear
};

// Mock current user
export const getCurrentUser = (): JwtResponse | null => {
  return {
    token: 'mock-token',
    type: 'Bearer',
    id: 1,
    username: 'System User',
    email: '<EMAIL>',
    roles: ['USER']
  };
};

// Always return true - no authentication required
export const isAuthenticated = (): boolean => {
  return true;
};

// No-op axios interceptor setup
export const setupAxiosInterceptors = (): void => {
  // No authentication headers needed
};
