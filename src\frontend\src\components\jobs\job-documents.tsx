"use client"

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Download, Eye, FileText, Image, PaperclipIcon as PaperClip, Loader2, AlertCircle, CheckCircle2, Info } from "lucide-react"
import { checkJobEligibility, triggerVerification } from "@/services/verificationService";
import { getJobDocuments } from "@/lib/api";

const API_BASE_URL = '/api';

// Define the type for the eligibility check response
interface EligibilityCheckResponse {
  isEligible: boolean;
  jobNo: string;
  jobTitle?: string;
  customerName?: string;
  message: string;
}

// Define the type for job documents
interface JobDocument {
  id: number;
  jobNo: string;
  documentType: string;
  classifiedDocumentType?: string;
  fileName: string;
  contentType: string;
  sourceUrl?: string;
  createdAt: string;
  fileSizeBytes: number;
  displayName: string;
  status: string;
  comment: string;
  fileExtension: string;
  pdf: boolean;
  image: boolean;
  formattedFileSize: string;
}

interface JobDocumentsProps {
  jobNo: string;
}

export function JobDocuments({ jobNo }: JobDocumentsProps) {
  console.log("Rendering documents for jobNo:", jobNo);

  // State for eligibility check
  const [eligibilityStatus, setEligibilityStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [eligibilityData, setEligibilityData] = useState<EligibilityCheckResponse | null>(null);
  const [eligibilityError, setEligibilityError] = useState<string | null>(null);

  // State for triggering verification
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationTriggered, setVerificationTriggered] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);

  // State for documents
  const [documentsStatus, setDocumentsStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [documents, setDocuments] = useState<JobDocument[]>([]);
  const [documentsError, setDocumentsError] = useState<string | null>(null);

  // useEffect to check eligibility and fetch documents when jobNo changes
  useEffect(() => {
    if (!jobNo) {
      setEligibilityStatus('idle');
      setEligibilityData(null);
      setEligibilityError(null);
      return;
    }

    const fetchEligibility = async () => {
      setEligibilityStatus('loading');
      setEligibilityData(null);
      setEligibilityError(null);
      setVerificationTriggered(false);
      setVerificationError(null);
      try {
        console.log(`Checking eligibility for job: ${jobNo}`);
        const data = await checkJobEligibility(jobNo);
        console.log('Eligibility check response:', data);
        setEligibilityData(data);
        setEligibilityStatus('success');
      } catch (error: any) {
        console.error('Error checking job eligibility:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to check eligibility';
        setEligibilityError(errorMessage);
        setEligibilityStatus('error');
      }
    };

    const fetchDocuments = async () => {
      setDocumentsStatus('loading');
      setDocuments([]);
      setDocumentsError(null);
      try {
        console.log(`Fetching documents for job: ${jobNo}`);
        const documentsData = await getJobDocuments(jobNo);
        console.log('Documents response:', documentsData);
        setDocuments(documentsData || []);
        setDocumentsStatus('success');
      } catch (error: any) {
        console.error('Error fetching job documents:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch documents';
        setDocumentsError(errorMessage);
        setDocumentsStatus('error');
      }
    };

    fetchEligibility();
    fetchDocuments();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobNo]);

  // Handler for triggering verification
  const handleVerifyDocuments = async () => {
    if (!jobNo) return;

    setIsVerifying(true);
    setVerificationError(null);
    setVerificationTriggered(false);

    try {
      console.log(`Triggering verification for job: ${jobNo}`);
      const response = await triggerVerification(jobNo);
      console.log('Verification trigger response:', response);
      setVerificationTriggered(true);
      // TODO: Optionally start polling for verification status here
    } catch (error: any) {
      console.error('Error triggering verification:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to trigger verification';
      setVerificationError(errorMessage);
    } finally {
      setIsVerifying(false);
    }
  };

  const getDocumentIcon = (document: JobDocument) => {
    if (document.pdf) {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (document.image) {
      return <Image className="h-5 w-5 text-blue-500" />;
    } else {
      return <PaperClip className="h-5 w-5" />;
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Ready":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "Available":
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  }

  const handleViewDocument = (documentId: number) => {
    const url = `${API_BASE_URL}/jobs/documents/view/${documentId}`;
    window.open(url, '_blank');
  };

  const handleDownloadDocument = (documentId: number, fileName: string) => {
    const url = `${API_BASE_URL}/jobs/documents/download/${documentId}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {eligibilityStatus === 'success' && eligibilityData?.jobTitle
            ? `${eligibilityData.jobTitle} (${jobNo})`
            : `Job Documents (${jobNo})`}
        </CardTitle>
        {eligibilityStatus === 'success' && eligibilityData?.customerName && (
          <p className="text-sm text-muted-foreground">
            Customer: {eligibilityData.customerName}
          </p>
        )}
      </CardHeader>
      <CardContent>
         {/* --- Business Central Workflow Status --- */}
         <div className="mb-4">
          {eligibilityStatus === 'loading' && (
            <div className="flex items-center text-muted-foreground">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Checking Business Central status...
            </div>
          )}
          {eligibilityStatus === 'error' && eligibilityError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Business Central Check Failed</AlertTitle>
              <AlertDescription>{eligibilityError}</AlertDescription>
            </Alert>
          )}
          {eligibilityStatus === 'success' && eligibilityData && !eligibilityData.isEligible && (
            // Only show if NOT eligible (has issues)
            <Alert variant="default">
              <Info className="h-4 w-4 text-yellow-500" />
              <AlertTitle>
                Business Central Workflow: Not Ready for Second Check
              </AlertTitle>
              <AlertDescription>
                {eligibilityData.message}
              </AlertDescription>
            </Alert>
          )}
        </div>
        {/* --- End Eligibility Check Display --- */}

        {/* --- Verification Trigger Feedback --- */}
        {verificationTriggered && (
          // Use "default" variant for success message
          <Alert variant="default" className="mb-4">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <AlertTitle>Verification Started</AlertTitle>
            <AlertDescription>
              The document verification process has been initiated for Job {jobNo}. You can check the status later.
            </AlertDescription>
          </Alert>
        )}
        {verificationError && (
           <Alert variant="destructive" className="mb-4">
             <AlertCircle className="h-4 w-4" />
             <AlertTitle>Verification Trigger Failed</AlertTitle>
             <AlertDescription>{verificationError}</AlertDescription>
           </Alert>
        )}
         {/* --- End Verification Trigger Feedback --- */}

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Job Documents</h3>
            {documentsStatus === 'loading' && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
          </div>

          {documentsStatus === 'error' && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error Loading Documents</AlertTitle>
              <AlertDescription>{documentsError}</AlertDescription>
            </Alert>
          )}

          {documentsStatus === 'success' && documents.length === 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>No Documents Found</AlertTitle>
              <AlertDescription>
                No documents have been uploaded or downloaded for this job yet.
              </AlertDescription>
            </Alert>
          )}

          {documentsStatus === 'success' && documents.length > 0 && (
            <div className="space-y-3">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-start justify-between rounded-lg border p-4"
                >
                  <div className="flex items-start gap-3 flex-1">
                    {getDocumentIcon(doc)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="font-medium truncate">{doc.displayName}</div>
                        {getStatusIcon(doc.status)}
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">
                        {doc.fileName} • {doc.formattedFileSize}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {doc.comment}
                      </div>
                      {doc.createdAt && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Added: {new Date(doc.createdAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2 ml-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      title="View document"
                      onClick={() => handleViewDocument(doc.id)}
                    >
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View document</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      title="Download document"
                      onClick={() => handleDownloadDocument(doc.id, doc.fileName)}
                    >
                      <Download className="h-4 w-4" />
                      <span className="sr-only">Download document</span>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* --- Verify Button --- */}
        {eligibilityStatus === 'success' && eligibilityData?.isEligible && !verificationTriggered && (
          <div className="mt-6 flex justify-end">
            <Button
              onClick={handleVerifyDocuments} // Should now resolve
              disabled={isVerifying}
            >
              {isVerifying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying...
                </>
              ) : (
                "Verify Documents"
              )}
            </Button>
          </div>
        )}
        {/* --- End Verify Button --- */}

      </CardContent>
    </Card>
  )
}
