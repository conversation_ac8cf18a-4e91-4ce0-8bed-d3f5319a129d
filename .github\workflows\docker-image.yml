name: Build, Test, and Push Docker Images

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build-test-push:
    runs-on: ubuntu-latest
    env:
      REGISTRY: ghcr.io
      IMAGE_FRONTEND: ghcr.io/${{ github.repository_owner }}/erp-frontend
      IMAGE_BACKEND_JAVA: ghcr.io/${{ github.repository_owner }}/erp-backend-java
      IMAGE_BACKEND_PYTHON: ghcr.io/${{ github.repository_owner }}/erp-backend-python

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push frontend
        uses: docker/build-push-action@v5
        with:
          context: ./src/frontend
          push: true
          tags: ${{ env.IMAGE_FRONTEND }}:latest

      - name: Build and push backend-java
        uses: docker/build-push-action@v5
        with:
          context: ./src/backend
          push: true
          tags: ${{ env.IMAGE_BACKEND_JAVA }}:latest

      - name: Build and push backend-python
        uses: docker/build-push-action@v5
        with:
          context: ./src/gemini-python-service
          push: true
          tags: ${{ env.IMAGE_BACKEND_PYTHON }}:latest

      - name: Install docker-compose
        run: sudo apt-get update && sudo apt-get install -y docker-compose

      - name: Compose up for integration test
        run: |
          docker-compose up -d
          sleep 20
          docker-compose ps
          docker-compose logs

      - name: Compose down
        run: docker-compose down
